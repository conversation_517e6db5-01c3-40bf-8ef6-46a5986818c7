import type { StackFrame } from 'next/dist/compiled/stacktrace-parser';
export type CodeFrameProps = {
    stackFrame: StackFrame;
    codeFrame: string;
};
export declare function CodeFrame({ stackFrame, codeFrame }: CodeFrameProps): import("react/jsx-runtime").JSX.Element;
export declare const CODE_FRAME_STYLES = "\n  [data-nextjs-codeframe] {\n    --code-frame-padding: 12px;\n    --code-frame-line-height: var(--size-16);\n    background-color: var(--color-background-200);\n    overflow: hidden;\n    color: var(--color-gray-1000);\n    text-overflow: ellipsis;\n    border: 1px solid var(--color-gray-400);\n    border-radius: 8px;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-12);\n    line-height: var(--code-frame-line-height);\n    margin: 8px 0;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n  }\n\n  .code-frame-link,\n  .code-frame-pre {\n    padding: var(--code-frame-padding);\n  }\n\n  .code-frame-link svg {\n    flex-shrink: 0;\n  }\n\n  .code-frame-link [data-text] {\n    display: inline-flex;\n    text-align: left;\n    margin: auto 6px;\n  }\n\n  .code-frame-header {\n    width: 100%;\n    transition: background 100ms ease-out;\n    border-radius: 8px 8px 0 0;\n    border-bottom: 1px solid var(--color-gray-400);\n  }\n\n  [data-with-open-in-editor-link-source-file] {\n    padding: 4px;\n    margin: -4px 0 -4px auto;\n    border-radius: var(--rounded-full);\n    margin-left: auto;\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  [data-nextjs-codeframe]::selection,\n  [data-nextjs-codeframe] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n\n  [data-nextjs-codeframe] *:not(a) {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n\n  [data-nextjs-codeframe-line][data-nextjs-codeframe-line--errored=\"true\"] {\n    position: relative;\n    isolation: isolate;\n\n    > span { \n      position: relative;\n      z-index: 1;\n    }\n\n    &::after {\n      content: \"\";\n      width: calc(100% + var(--code-frame-padding) * 2);\n      height: var(--code-frame-line-height);\n      left: calc(-1 * var(--code-frame-padding));\n      background: var(--color-red-200);\n      box-shadow: 2px 0 0 0 var(--color-red-900) inset;\n      position: absolute;\n    }\n  }\n\n\n  [data-nextjs-codeframe] > * {\n    margin: 0;\n  }\n\n  .code-frame-link {\n    display: flex;\n    margin: 0;\n    outline: 0;\n  }\n  .code-frame-link [data-icon='right'] {\n    margin-left: auto;\n  }\n\n  [data-nextjs-codeframe] div > pre {\n    overflow: hidden;\n    display: inline-block;\n  }\n\n  [data-nextjs-codeframe] svg {\n    color: var(--color-gray-900);\n  }\n";
