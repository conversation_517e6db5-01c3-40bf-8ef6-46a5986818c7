{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-[var(--color-border)] bg-[var(--background-primary)] shadow-[var(--shadow-sm)] transition-all duration-200\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight text-[var(--text-primary)]\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-[var(--text-secondary)]\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uIACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { \n  Building, \n  Users, \n  CreditCard, \n  TrendingUp, \n  AlertTriangle,\n  Calendar,\n  DollarSign,\n  Home\n} from 'lucide-react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { formatCurrency, formatDate } from '@/lib/utils';\n\nexport default function DashboardPage() {\n  // Mock data - em produção viria de uma API\n  const stats = {\n    totalProperties: 12,\n    occupiedProperties: 10,\n    totalRevenue: 45000,\n    pendingPayments: 3,\n    maintenanceRequests: 2,\n    occupancyRate: 83.3\n  };\n\n  const recentActivities = [\n    {\n      id: '1',\n      type: 'payment',\n      description: 'Pagamento recebido - Apt 101',\n      amount: 2500,\n      timestamp: new Date('2024-01-15T10:30:00'),\n      status: 'success'\n    },\n    {\n      id: '2',\n      type: 'maintenance',\n      description: 'Nova solicitação de manutenção - Casa 205',\n      timestamp: new Date('2024-01-15T09:15:00'),\n      status: 'pending'\n    },\n    {\n      id: '3',\n      type: 'contract',\n      description: 'Contrato renovado - Kitnet 302',\n      timestamp: new Date('2024-01-14T16:45:00'),\n      status: 'success'\n    },\n    {\n      id: '4',\n      type: 'payment',\n      description: 'Pagamento em atraso - Apt 203',\n      amount: 1800,\n      timestamp: new Date('2024-01-14T14:20:00'),\n      status: 'warning'\n    }\n  ];\n\n  const upcomingPayments = [\n    {\n      id: '1',\n      tenant: 'Maria Silva',\n      property: 'Apartamento 101',\n      amount: 2500,\n      dueDate: new Date('2024-01-20'),\n      status: 'pending'\n    },\n    {\n      id: '2',\n      tenant: 'João Santos',\n      property: 'Casa 205',\n      amount: 3200,\n      dueDate: new Date('2024-01-22'),\n      status: 'pending'\n    },\n    {\n      id: '3',\n      tenant: 'Ana Costa',\n      property: 'Kitnet 302',\n      amount: 1200,\n      dueDate: new Date('2024-01-25'),\n      status: 'pending'\n    }\n  ];\n\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'payment':\n        return <CreditCard className=\"h-4 w-4\" />;\n      case 'maintenance':\n        return <AlertTriangle className=\"h-4 w-4\" />;\n      case 'contract':\n        return <Users className=\"h-4 w-4\" />;\n      default:\n        return <Home className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'success':\n        return 'text-[var(--color-accent)]';\n      case 'warning':\n        return 'text-[var(--color-warning)]';\n      case 'pending':\n        return 'text-[var(--color-info)]';\n      default:\n        return 'text-[var(--text-secondary)]';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-[var(--text-primary)]\">Dashboard</h1>\n          <p className=\"text-[var(--text-secondary)] mt-1\">\n            Visão geral dos seus imóveis e atividades\n          </p>\n        </div>\n        <div className=\"mt-4 sm:mt-0\">\n          <Button>\n            <Building className=\"h-4 w-4 mr-2\" />\n            Nova Propriedade\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total de Propriedades</CardTitle>\n            <Building className=\"h-4 w-4 text-[var(--text-secondary)]\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-[var(--text-primary)]\">\n              {stats.totalProperties}\n            </div>\n            <p className=\"text-xs text-[var(--text-secondary)]\">\n              {stats.occupiedProperties} ocupadas\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Receita Total</CardTitle>\n            <DollarSign className=\"h-4 w-4 text-[var(--text-secondary)]\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-[var(--text-primary)]\">\n              {formatCurrency(stats.totalRevenue)}\n            </div>\n            <p className=\"text-xs text-[var(--color-accent)]\">\n              +12% em relação ao mês anterior\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Pagamentos Pendentes</CardTitle>\n            <CreditCard className=\"h-4 w-4 text-[var(--text-secondary)]\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-[var(--text-primary)]\">\n              {stats.pendingPayments}\n            </div>\n            <p className=\"text-xs text-[var(--color-warning)]\">\n              Requer atenção\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Taxa de Ocupação</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-[var(--text-secondary)]\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold text-[var(--text-primary)]\">\n              {stats.occupancyRate}%\n            </div>\n            <p className=\"text-xs text-[var(--color-accent)]\">\n              Acima da média\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Main Content Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Activities */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Atividades Recentes</CardTitle>\n            <CardDescription>\n              Últimas movimentações em suas propriedades\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {recentActivities.map((activity) => (\n                <div key={activity.id} className=\"flex items-center space-x-4\">\n                  <div className={`p-2 rounded-full bg-[var(--background-secondary)] ${getStatusColor(activity.status)}`}>\n                    {getActivityIcon(activity.type)}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-[var(--text-primary)] truncate\">\n                      {activity.description}\n                    </p>\n                    <p className=\"text-xs text-[var(--text-secondary)]\">\n                      {formatDate(activity.timestamp, { \n                        day: '2-digit', \n                        month: '2-digit', \n                        hour: '2-digit', \n                        minute: '2-digit' \n                      })}\n                    </p>\n                  </div>\n                  {activity.amount && (\n                    <div className=\"text-sm font-medium text-[var(--text-primary)]\">\n                      {formatCurrency(activity.amount)}\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Upcoming Payments */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Próximos Pagamentos</CardTitle>\n            <CardDescription>\n              Pagamentos esperados nos próximos dias\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {upcomingPayments.map((payment) => (\n                <div key={payment.id} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"p-2 rounded-full bg-[var(--color-primary)]/10\">\n                      <Calendar className=\"h-4 w-4 text-[var(--color-primary)]\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-[var(--text-primary)]\">\n                        {payment.tenant}\n                      </p>\n                      <p className=\"text-xs text-[var(--text-secondary)]\">\n                        {payment.property}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-sm font-medium text-[var(--text-primary)]\">\n                      {formatCurrency(payment.amount)}\n                    </p>\n                    <p className=\"text-xs text-[var(--text-secondary)]\">\n                      {formatDate(payment.dueDate)}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAdA;;;;;;AAgBe,SAAS;IACtB,2CAA2C;IAC3C,MAAM,QAAQ;QACZ,iBAAiB;QACjB,oBAAoB;QACpB,cAAc;QACd,iBAAiB;QACjB,qBAAqB;QACrB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,WAAW,IAAI,KAAK;YACpB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW,IAAI,KAAK;YACpB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,WAAW,IAAI,KAAK;YACpB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,WAAW,IAAI,KAAK;YACpB,QAAQ;QACV;KACD;IAED,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS,IAAI,KAAK;YAClB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS,IAAI,KAAK;YAClB,QAAQ;QACV;QACA;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS,IAAI,KAAK;YAClB,QAAQ;QACV;KACD;IAED,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,sMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAC9D,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;kCAInD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,MAAM,eAAe;;;;;;kDAExB,6LAAC;wCAAE,WAAU;;4CACV,MAAM,kBAAkB;4CAAC;;;;;;;;;;;;;;;;;;;kCAKhC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;kDAEpC,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;kCAMtD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDACZ,MAAM,eAAe;;;;;;kDAExB,6LAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;kCAMvD,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,aAAa;4CAAC;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDAAI,WAAW,CAAC,kDAAkD,EAAE,eAAe,SAAS,MAAM,GAAG;8DACnG,gBAAgB,SAAS,IAAI;;;;;;8DAEhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS,EAAE;gEAC9B,KAAK;gEACL,OAAO;gEACP,MAAM;gEACN,QAAQ;4DACV;;;;;;;;;;;;gDAGH,SAAS,MAAM,kBACd,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,MAAM;;;;;;;2CAnB3B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;kCA6B7B,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EACV,QAAQ,MAAM;;;;;;8EAEjB,6LAAC;oEAAE,WAAU;8EACV,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;8DAIvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;sEAEhC,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,OAAO;;;;;;;;;;;;;2CAnBvB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BpC;KApQwB", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///home/<USER>/workspaces/saas/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///home/<USER>/workspaces/saas/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///home/<USER>/workspaces/saas/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///home/<USER>/workspaces/saas/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}