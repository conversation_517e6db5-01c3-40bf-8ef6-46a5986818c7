{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\n/**\n * Utility function to merge class names with clsx\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format currency values\n */\nexport function formatCurrency(value: number, currency = 'BRL'): string {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency,\n  }).format(value);\n}\n\n/**\n * Format date values\n */\nexport function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    ...options,\n  }).format(dateObj);\n}\n\n/**\n * Format relative time (e.g., \"2 days ago\")\n */\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n\n  const intervals = [\n    { label: 'ano', seconds: 31536000 },\n    { label: 'mês', seconds: 2592000 },\n    { label: 'semana', seconds: 604800 },\n    { label: 'dia', seconds: 86400 },\n    { label: 'hora', seconds: 3600 },\n    { label: 'minuto', seconds: 60 },\n  ];\n\n  for (const interval of intervals) {\n    const count = Math.floor(diffInSeconds / interval.seconds);\n    if (count >= 1) {\n      return `há ${count} ${interval.label}${count > 1 ? 's' : ''}`;\n    }\n  }\n\n  return 'agora mesmo';\n}\n\n/**\n * Generate a random ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n\n/**\n * Validate email format\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Validate CPF format (Brazilian tax ID)\n */\nexport function isValidCPF(cpf: string): boolean {\n  const cleanCPF = cpf.replace(/\\D/g, '');\n  \n  if (cleanCPF.length !== 11) return false;\n  if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false;\n\n  let sum = 0;\n  for (let i = 0; i < 9; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);\n  }\n  let remainder = (sum * 10) % 11;\n  if (remainder === 10 || remainder === 11) remainder = 0;\n  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;\n\n  sum = 0;\n  for (let i = 0; i < 10; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);\n  }\n  remainder = (sum * 10) % 11;\n  if (remainder === 10 || remainder === 11) remainder = 0;\n  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;\n\n  return true;\n}\n\n/**\n * Format CPF\n */\nexport function formatCPF(cpf: string): string {\n  const cleanCPF = cpf.replace(/\\D/g, '');\n  return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, '$1.$2.$3-$4');\n}\n\n/**\n * Format phone number\n */\nexport function formatPhone(phone: string): string {\n  const cleanPhone = phone.replace(/\\D/g, '');\n  if (cleanPhone.length === 11) {\n    return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n  }\n  if (cleanPhone.length === 10) {\n    return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n  }\n  return phone;\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Sleep function for async operations\n */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,eAAe,KAAa,EAAE,WAAW,KAAK;IAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,WAAW,IAAmB,EAAE,OAAoC;IAClF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,GAAG,OAAO;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,MAAM,YAAY;QAChB;YAAE,OAAO;YAAO,SAAS;QAAS;QAClC;YAAE,OAAO;YAAO,SAAS;QAAQ;QACjC;YAAE,OAAO;YAAU,SAAS;QAAO;QACnC;YAAE,OAAO;YAAO,SAAS;QAAM;QAC/B;YAAE,OAAO;YAAQ,SAAS;QAAK;QAC/B;YAAE,OAAO;YAAU,SAAS;QAAG;KAChC;IAED,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB,SAAS,OAAO;QACzD,IAAI,SAAS,GAAG;YACd,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,SAAS,KAAK,GAAG,QAAQ,IAAI,MAAM,IAAI;QAC/D;IACF;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,WAAW,GAAW;IACpC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IAEpC,IAAI,SAAS,MAAM,KAAK,IAAI,OAAO;IACnC,IAAI,eAAe,IAAI,CAAC,WAAW,OAAO;IAE1C,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,IAAI,YAAY,AAAC,MAAM,KAAM;IAC7B,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,KAAK,OAAO;IAEvD,MAAM;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,YAAY,AAAC,MAAM,KAAM;IACzB,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,MAAM,OAAO;IAExD,OAAO;AACT;AAKO,SAAS,UAAU,GAAW;IACnC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IACpC,OAAO,SAAS,OAAO,CAAC,gCAAgC;AAC1D;AAKO,SAAS,YAAY,KAAa;IACvC,MAAM,aAAa,MAAM,OAAO,CAAC,OAAO;IACxC,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,OAAO;AACT;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    \n    const variants = {\n      primary: \"bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90 focus-visible:ring-[var(--color-primary)]\",\n      secondary: \"bg-[var(--color-secondary)] text-white hover:bg-[var(--color-secondary)]/90 focus-visible:ring-[var(--color-secondary)]\",\n      outline: \"border border-[var(--color-border)] bg-transparent text-[var(--text-primary)] hover:bg-[var(--surface-hover)] focus-visible:ring-[var(--color-primary)]\",\n      ghost: \"bg-transparent text-[var(--text-primary)] hover:bg-[var(--surface-hover)] focus-visible:ring-[var(--color-primary)]\",\n      destructive: \"bg-[var(--color-error)] text-white hover:bg-[var(--color-error)]/90 focus-visible:ring-[var(--color-error)]\",\n    };\n\n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, id, ...props }, ref) => {\n    const inputId = id || React.useId();\n\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"text-sm font-medium text-[var(--text-primary)] block\"\n          >\n            {label}\n            {props.required && <span className=\"text-[var(--color-error)] ml-1\">*</span>}\n          </label>\n        )}\n        <input\n          type={type}\n          id={inputId}\n          className={cn(\n            \"flex h-10 w-full rounded-lg border border-[var(--color-border)] bg-[var(--background-primary)] px-3 py-2 text-sm text-[var(--text-primary)] placeholder:text-[var(--text-secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200\",\n            error && \"border-[var(--color-error)] focus:ring-[var(--color-error)]\",\n            className\n          )}\n          ref={ref}\n          aria-invalid={error ? \"true\" : \"false\"}\n          aria-describedby={\n            error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined\n          }\n          {...props}\n        />\n        {error && (\n          <p\n            id={`${inputId}-error`}\n            className=\"text-sm text-[var(--color-error)]\"\n            role=\"alert\"\n          >\n            {error}\n          </p>\n        )}\n        {helperText && !error && (\n          <p\n            id={`${inputId}-helper`}\n            className=\"text-sm text-[var(--text-secondary)]\"\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAEhC,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;kCAAiC;;;;;;;;;;;;0BAGxE,8OAAC;gBACC,MAAM;gBACN,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+VACA,SAAS,+DACT;gBAEF,KAAK;gBACL,gBAAc,QAAQ,SAAS;gBAC/B,oBACE,QAAQ,GAAG,QAAQ,MAAM,CAAC,GAAG,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;gBAEjE,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBACC,IAAI,GAAG,QAAQ,MAAM,CAAC;gBACtB,WAAU;gBACV,MAAK;0BAEJ;;;;;;YAGJ,cAAc,CAAC,uBACd,8OAAC;gBACC,IAAI,GAAG,QAAQ,OAAO,CAAC;gBACvB,WAAU;0BAET;;;;;;;;;;;;AAKX;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-[var(--color-border)] bg-[var(--background-primary)] shadow-[var(--shadow-sm)] transition-all duration-200\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight text-[var(--text-primary)]\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-[var(--text-secondary)]\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Moon, Sun, Monitor } from 'lucide-react';\nimport { Button } from './button';\n\nexport function ThemeToggle() {\n  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system';\n    if (savedTheme) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    let newTheme: 'light' | 'dark' | 'system';\n\n    if (theme === 'light') {\n      newTheme = 'dark';\n    } else if (theme === 'dark') {\n      newTheme = 'system';\n    } else {\n      newTheme = 'light';\n    }\n\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n\n    // Apply theme\n    const root = document.documentElement;\n    root.removeAttribute('data-theme');\n\n    if (newTheme === 'dark') {\n      root.setAttribute('data-theme', 'dark');\n    } else if (newTheme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      if (systemTheme === 'dark') {\n        root.setAttribute('data-theme', 'dark');\n      }\n    }\n  };\n\n  const getIcon = () => {\n    if (!mounted) return <Sun className=\"h-4 w-4\" />;\n\n    if (theme === 'system') {\n      return <Monitor className=\"h-4 w-4\" />;\n    }\n    return theme === 'dark' ? <Moon className=\"h-4 w-4\" /> : <Sun className=\"h-4 w-4\" />;\n  };\n\n  const getLabel = () => {\n    if (!mounted) return 'Claro';\n\n    if (theme === 'system') {\n      return 'Sistema';\n    }\n    return theme === 'dark' ? 'Escuro' : 'Claro';\n  };\n\n  if (!mounted) {\n    return (\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"w-auto px-3\"\n        disabled\n      >\n        <Sun className=\"h-4 w-4\" />\n        <span className=\"ml-2 hidden sm:inline\">Claro</span>\n      </Button>\n    );\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"sm\"\n      onClick={toggleTheme}\n      className=\"w-auto px-3\"\n      aria-label={`Alternar tema. Tema atual: ${getLabel()}`}\n    >\n      {getIcon()}\n      <span className=\"ml-2 hidden sm:inline\">{getLabel()}</span>\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,SAAS;QACX;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;QAEJ,IAAI,UAAU,SAAS;YACrB,WAAW;QACb,OAAO,IAAI,UAAU,QAAQ;YAC3B,WAAW;QACb,OAAO;YACL,WAAW;QACb;QAEA,SAAS;QACT,aAAa,OAAO,CAAC,SAAS;QAE9B,cAAc;QACd,MAAM,OAAO,SAAS,eAAe;QACrC,KAAK,eAAe,CAAC;QAErB,IAAI,aAAa,QAAQ;YACvB,KAAK,YAAY,CAAC,cAAc;QAClC,OAAO,IAAI,aAAa,UAAU;YAChC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YACzF,IAAI,gBAAgB,QAAQ;gBAC1B,KAAK,YAAY,CAAC,cAAc;YAClC;QACF;IACF;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,SAAS,qBAAO,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QAEpC,IAAI,UAAU,UAAU;YACtB,qBAAO,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QACA,OAAO,UAAU,uBAAS,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;iCAAe,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IAC1E;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,SAAS,OAAO;QAErB,IAAI,UAAU,UAAU;YACtB,OAAO;QACT;QACA,OAAO,UAAU,SAAS,WAAW;IACvC;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,QAAQ;;8BAER,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;QACV,cAAY,CAAC,2BAA2B,EAAE,YAAY;;YAErD;0BACD,8OAAC;gBAAK,WAAU;0BAAyB;;;;;;;;;;;;AAG/C", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/app/%28auth%29/register/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Building, Eye, EyeOff, User, Home } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\nimport { isValidEmail, isValidCPF, formatCPF, formatPhone } from '@/lib/utils';\nimport type { RegisterForm } from '@/types';\n\nexport default function RegisterPage() {\n  const router = useRouter();\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState<RegisterForm>({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'owner',\n    phone: '',\n    cpf: ''\n  });\n  const [errors, setErrors] = useState<Partial<RegisterForm>>({});\n\n  const handleInputChange = (field: keyof RegisterForm, value: string) => {\n    let processedValue = value;\n    \n    // Format specific fields\n    if (field === 'cpf') {\n      processedValue = formatCPF(value);\n    } else if (field === 'phone') {\n      processedValue = formatPhone(value);\n    }\n    \n    setFormData(prev => ({ ...prev, [field]: processedValue }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<RegisterForm> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Nome é obrigatório';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Nome deve ter pelo menos 2 caracteres';\n    }\n\n    if (!formData.email) {\n      newErrors.email = 'Email é obrigatório';\n    } else if (!isValidEmail(formData.email)) {\n      newErrors.email = 'Email inválido';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Senha é obrigatória';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Senha deve ter pelo menos 6 caracteres';\n    }\n\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Confirmação de senha é obrigatória';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Senhas não coincidem';\n    }\n\n    if (formData.cpf && !isValidCPF(formData.cpf)) {\n      newErrors.cpf = 'CPF inválido';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    setLoading(true);\n    \n    try {\n      // Simular chamada de API\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Em produção, aqui seria feita a criação da conta\n      console.log('Register attempt:', formData);\n      \n      // Redirecionar para dashboard\n      router.push('/dashboard');\n    } catch (error) {\n      console.error('Register error:', error);\n      setErrors({ email: 'Erro ao criar conta. Tente novamente.' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-[var(--background-primary)] flex flex-col\">\n      {/* Header */}\n      <header className=\"border-b border-[var(--color-border)]\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-lg bg-[var(--color-primary)] flex items-center justify-center\">\n                <Building className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-[var(--text-primary)]\">\n                RentManager\n              </span>\n            </Link>\n            <ThemeToggle />\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex items-center justify-center p-4\">\n        <div className=\"w-full max-w-md\">\n          <Card>\n            <CardHeader className=\"text-center\">\n              <CardTitle className=\"text-2xl\">Criar sua conta</CardTitle>\n              <CardDescription>\n                Preencha os dados para começar a usar o RentManager\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <Input\n                  label=\"Nome completo\"\n                  type=\"text\"\n                  placeholder=\"Seu nome completo\"\n                  value={formData.name}\n                  onChange={(e) => handleInputChange('name', e.target.value)}\n                  error={errors.name}\n                  required\n                />\n\n                <Input\n                  label=\"Email\"\n                  type=\"email\"\n                  placeholder=\"<EMAIL>\"\n                  value={formData.email}\n                  onChange={(e) => handleInputChange('email', e.target.value)}\n                  error={errors.email}\n                  required\n                />\n\n                <div className=\"relative\">\n                  <Input\n                    label=\"Senha\"\n                    type={showPassword ? 'text' : 'password'}\n                    placeholder=\"Sua senha\"\n                    value={formData.password}\n                    onChange={(e) => handleInputChange('password', e.target.value)}\n                    error={errors.password}\n                    required\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-[38px] text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors\"\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-4 w-4\" />\n                    ) : (\n                      <Eye className=\"h-4 w-4\" />\n                    )}\n                  </button>\n                </div>\n\n                <div className=\"relative\">\n                  <Input\n                    label=\"Confirmar senha\"\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    placeholder=\"Confirme sua senha\"\n                    value={formData.confirmPassword}\n                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}\n                    error={errors.confirmPassword}\n                    required\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    className=\"absolute right-3 top-[38px] text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors\"\n                  >\n                    {showConfirmPassword ? (\n                      <EyeOff className=\"h-4 w-4\" />\n                    ) : (\n                      <Eye className=\"h-4 w-4\" />\n                    )}\n                  </button>\n                </div>\n\n                {/* Role Selection */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-[var(--text-primary)] block\">\n                    Tipo de usuário\n                  </label>\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    <button\n                      type=\"button\"\n                      onClick={() => handleInputChange('role', 'owner')}\n                      className={`p-3 rounded-lg border-2 transition-all duration-200 ${\n                        formData.role === 'owner'\n                          ? 'border-[var(--color-primary)] bg-[var(--color-primary)]/10'\n                          : 'border-[var(--color-border)] hover:border-[var(--color-primary)]/50'\n                      }`}\n                    >\n                      <Home className=\"h-5 w-5 mx-auto mb-1 text-[var(--color-primary)]\" />\n                      <span className=\"text-sm font-medium text-[var(--text-primary)]\">\n                        Proprietário\n                      </span>\n                    </button>\n                    <button\n                      type=\"button\"\n                      onClick={() => handleInputChange('role', 'tenant')}\n                      className={`p-3 rounded-lg border-2 transition-all duration-200 ${\n                        formData.role === 'tenant'\n                          ? 'border-[var(--color-primary)] bg-[var(--color-primary)]/10'\n                          : 'border-[var(--color-border)] hover:border-[var(--color-primary)]/50'\n                      }`}\n                    >\n                      <User className=\"h-5 w-5 mx-auto mb-1 text-[var(--color-primary)]\" />\n                      <span className=\"text-sm font-medium text-[var(--text-primary)]\">\n                        Inquilino\n                      </span>\n                    </button>\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <Input\n                    label=\"Telefone\"\n                    type=\"tel\"\n                    placeholder=\"(11) 99999-9999\"\n                    value={formData.phone}\n                    onChange={(e) => handleInputChange('phone', e.target.value)}\n                    error={errors.phone}\n                  />\n\n                  <Input\n                    label=\"CPF\"\n                    type=\"text\"\n                    placeholder=\"000.000.000-00\"\n                    value={formData.cpf}\n                    onChange={(e) => handleInputChange('cpf', e.target.value)}\n                    error={errors.cpf}\n                  />\n                </div>\n\n                <div className=\"flex items-start space-x-2\">\n                  <input\n                    type=\"checkbox\"\n                    required\n                    className=\"mt-1 rounded border-[var(--color-border)] text-[var(--color-primary)] focus:ring-[var(--color-primary)]\"\n                  />\n                  <label className=\"text-sm text-[var(--text-secondary)]\">\n                    Aceito os{' '}\n                    <Link href=\"/terms\" className=\"text-[var(--color-primary)] hover:underline\">\n                      termos de uso\n                    </Link>{' '}\n                    e{' '}\n                    <Link href=\"/privacy\" className=\"text-[var(--color-primary)] hover:underline\">\n                      política de privacidade\n                    </Link>\n                  </label>\n                </div>\n\n                <Button\n                  type=\"submit\"\n                  className=\"w-full\"\n                  loading={loading}\n                  disabled={loading}\n                >\n                  {loading ? 'Criando conta...' : 'Criar conta'}\n                </Button>\n              </form>\n\n              <div className=\"mt-6 text-center\">\n                <p className=\"text-sm text-[var(--text-secondary)]\">\n                  Já tem uma conta?{' '}\n                  <Link\n                    href=\"/login\"\n                    className=\"text-[var(--color-primary)] hover:underline font-medium\"\n                  >\n                    Fazer login\n                  </Link>\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACrD,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,MAAM;QACN,OAAO;QACP,KAAK;IACP;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IAE7D,MAAM,oBAAoB,CAAC,OAA2B;QACpD,IAAI,iBAAiB;QAErB,yBAAyB;QACzB,IAAI,UAAU,OAAO;YACnB,iBAAiB,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE;QAC7B,OAAO,IAAI,UAAU,SAAS;YAC5B,iBAAiB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;QAC/B;QAEA,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAe,CAAC;QAEzD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACpD;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAmC,CAAC;QAE1C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;YAC1C,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,KAAK,GAAG;YACxC,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YACvC,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,UAAU,eAAe,GAAG;QAC9B,OAAO,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YACzD,UAAU,eAAe,GAAG;QAC9B;QAEA,IAAI,SAAS,GAAG,IAAI,CAAC,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,GAAG,GAAG;YAC7C,UAAU,GAAG,GAAG;QAClB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QAEX,IAAI;YACF,yBAAyB;YACzB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,mDAAmD;YACnD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,8BAA8B;YAC9B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,UAAU;gBAAE,OAAO;YAAwC;QAC7D,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAIjE,8OAAC,2IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;kDAChC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,OAAO,OAAO,IAAI;gDAClB,QAAQ;;;;;;0DAGV,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,OAAO,OAAO,KAAK;gDACnB,QAAQ;;;;;;0DAGV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAM;wDACN,MAAM,eAAe,SAAS;wDAC9B,aAAY;wDACZ,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,OAAO,OAAO,QAAQ;wDACtB,QAAQ;;;;;;kEAEV,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,gBAAgB,CAAC;wDAChC,WAAU;kEAET,6BACC,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAKrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAM;wDACN,MAAM,sBAAsB,SAAS;wDACrC,aAAY;wDACZ,OAAO,SAAS,eAAe;wDAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACpE,OAAO,OAAO,eAAe;wDAC7B,QAAQ;;;;;;kEAEV,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,uBAAuB,CAAC;wDACvC,WAAU;kEAET,oCACC,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAMrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAuD;;;;;;kEAGxE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,kBAAkB,QAAQ;gEACzC,WAAW,CAAC,oDAAoD,EAC9D,SAAS,IAAI,KAAK,UACd,+DACA,uEACJ;;kFAEF,8OAAC,mMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAiD;;;;;;;;;;;;0EAInE,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,kBAAkB,QAAQ;gEACzC,WAAW,CAAC,oDAAoD,EAC9D,SAAS,IAAI,KAAK,WACd,+DACA,uEACJ;;kFAEF,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAiD;;;;;;;;;;;;;;;;;;;;;;;;0DAOvE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAM;wDACN,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC1D,OAAO,OAAO,KAAK;;;;;;kEAGrB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAM;wDACN,MAAK;wDACL,aAAY;wDACZ,OAAO,SAAS,GAAG;wDACnB,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;wDACxD,OAAO,OAAO,GAAG;;;;;;;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,WAAU;;4DAAuC;4DAC5C;0EACV,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA8C;;;;;;4DAEpE;4DAAI;4DACV;0EACF,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA8C;;;;;;;;;;;;;;;;;;0DAMlF,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;0DAET,UAAU,qBAAqB;;;;;;;;;;;;kDAIpC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAAuC;gDAChC;8DAClB,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}