{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/lib/theme.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useEffect, useState } from 'react';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  resolvedTheme: 'light' | 'dark';\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\n  const [theme, setTheme] = useState<Theme>('system');\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');\n\n  useEffect(() => {\n    // Get theme from localStorage or default to system\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    const root = document.documentElement;\n    \n    // Remove previous theme classes\n    root.removeAttribute('data-theme');\n    \n    let effectiveTheme: 'light' | 'dark';\n    \n    if (theme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      effectiveTheme = systemTheme;\n    } else {\n      effectiveTheme = theme;\n    }\n    \n    // Apply theme\n    if (effectiveTheme === 'dark') {\n      root.setAttribute('data-theme', 'dark');\n    }\n    \n    setResolvedTheme(effectiveTheme);\n    \n    // Save to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = () => {\n      if (theme === 'system') {\n        const systemTheme = mediaQuery.matches ? 'dark' : 'light';\n        setResolvedTheme(systemTheme);\n        \n        const root = document.documentElement;\n        root.removeAttribute('data-theme');\n        if (systemTheme === 'dark') {\n          root.setAttribute('data-theme', 'dark');\n        }\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,SAAS,eAAe;QAErC,gCAAgC;QAChC,KAAK,eAAe,CAAC;QAErB,IAAI;QAEJ,IAAI,UAAU,UAAU;YACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YACzF,iBAAiB;QACnB,OAAO;YACL,iBAAiB;QACnB;QAEA,cAAc;QACd,IAAI,mBAAmB,QAAQ;YAC7B,KAAK,YAAY,CAAC,cAAc;QAClC;QAEA,iBAAiB;QAEjB,uBAAuB;QACvB,aAAa,OAAO,CAAC,SAAS;IAChC,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QAErC,MAAM,eAAe;YACnB,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,WAAW,OAAO,GAAG,SAAS;gBAClD,iBAAiB;gBAEjB,MAAM,OAAO,SAAS,eAAe;gBACrC,KAAK,eAAe,CAAC;gBACrB,IAAI,gBAAgB,QAAQ;oBAC1B,KAAK,YAAY,CAAC,cAAc;gBAClC;YACF;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAc;kBAC5D;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}