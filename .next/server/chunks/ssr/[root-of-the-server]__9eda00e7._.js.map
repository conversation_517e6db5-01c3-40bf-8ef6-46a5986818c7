{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\n/**\n * Utility function to merge class names with clsx\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format currency values\n */\nexport function formatCurrency(value: number, currency = 'BRL'): string {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency,\n  }).format(value);\n}\n\n/**\n * Format date values\n */\nexport function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    ...options,\n  }).format(dateObj);\n}\n\n/**\n * Format relative time (e.g., \"2 days ago\")\n */\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n\n  const intervals = [\n    { label: 'ano', seconds: 31536000 },\n    { label: 'mês', seconds: 2592000 },\n    { label: 'semana', seconds: 604800 },\n    { label: 'dia', seconds: 86400 },\n    { label: 'hora', seconds: 3600 },\n    { label: 'minuto', seconds: 60 },\n  ];\n\n  for (const interval of intervals) {\n    const count = Math.floor(diffInSeconds / interval.seconds);\n    if (count >= 1) {\n      return `há ${count} ${interval.label}${count > 1 ? 's' : ''}`;\n    }\n  }\n\n  return 'agora mesmo';\n}\n\n/**\n * Generate a random ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n\n/**\n * Validate email format\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Validate CPF format (Brazilian tax ID)\n */\nexport function isValidCPF(cpf: string): boolean {\n  const cleanCPF = cpf.replace(/\\D/g, '');\n  \n  if (cleanCPF.length !== 11) return false;\n  if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false;\n\n  let sum = 0;\n  for (let i = 0; i < 9; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);\n  }\n  let remainder = (sum * 10) % 11;\n  if (remainder === 10 || remainder === 11) remainder = 0;\n  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;\n\n  sum = 0;\n  for (let i = 0; i < 10; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);\n  }\n  remainder = (sum * 10) % 11;\n  if (remainder === 10 || remainder === 11) remainder = 0;\n  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;\n\n  return true;\n}\n\n/**\n * Format CPF\n */\nexport function formatCPF(cpf: string): string {\n  const cleanCPF = cpf.replace(/\\D/g, '');\n  return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, '$1.$2.$3-$4');\n}\n\n/**\n * Format phone number\n */\nexport function formatPhone(phone: string): string {\n  const cleanPhone = phone.replace(/\\D/g, '');\n  if (cleanPhone.length === 11) {\n    return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n  }\n  if (cleanPhone.length === 10) {\n    return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n  }\n  return phone;\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Sleep function for async operations\n */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,eAAe,KAAa,EAAE,WAAW,KAAK;IAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,WAAW,IAAmB,EAAE,OAAoC;IAClF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,GAAG,OAAO;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,MAAM,YAAY;QAChB;YAAE,OAAO;YAAO,SAAS;QAAS;QAClC;YAAE,OAAO;YAAO,SAAS;QAAQ;QACjC;YAAE,OAAO;YAAU,SAAS;QAAO;QACnC;YAAE,OAAO;YAAO,SAAS;QAAM;QAC/B;YAAE,OAAO;YAAQ,SAAS;QAAK;QAC/B;YAAE,OAAO;YAAU,SAAS;QAAG;KAChC;IAED,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB,SAAS,OAAO;QACzD,IAAI,SAAS,GAAG;YACd,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,SAAS,KAAK,GAAG,QAAQ,IAAI,MAAM,IAAI;QAC/D;IACF;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,WAAW,GAAW;IACpC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IAEpC,IAAI,SAAS,MAAM,KAAK,IAAI,OAAO;IACnC,IAAI,eAAe,IAAI,CAAC,WAAW,OAAO;IAE1C,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,IAAI,YAAY,AAAC,MAAM,KAAM;IAC7B,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,KAAK,OAAO;IAEvD,MAAM;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,YAAY,AAAC,MAAM,KAAM;IACzB,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,MAAM,OAAO;IAExD,OAAO;AACT;AAKO,SAAS,UAAU,GAAW;IACnC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IACpC,OAAO,SAAS,OAAO,CAAC,gCAAgC;AAC1D;AAKO,SAAS,YAAY,KAAa;IACvC,MAAM,aAAa,MAAM,OAAO,CAAC,OAAO;IACxC,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,OAAO;AACT;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    \n    const variants = {\n      primary: \"bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90 focus-visible:ring-[var(--color-primary)]\",\n      secondary: \"bg-[var(--color-secondary)] text-white hover:bg-[var(--color-secondary)]/90 focus-visible:ring-[var(--color-secondary)]\",\n      outline: \"border border-[var(--color-border)] bg-transparent text-[var(--text-primary)] hover:bg-[var(--surface-hover)] focus-visible:ring-[var(--color-primary)]\",\n      ghost: \"bg-transparent text-[var(--text-primary)] hover:bg-[var(--surface-hover)] focus-visible:ring-[var(--color-primary)]\",\n      destructive: \"bg-[var(--color-error)] text-white hover:bg-[var(--color-error)]/90 focus-visible:ring-[var(--color-error)]\",\n    };\n\n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Moon, Sun, Monitor } from 'lucide-react';\nimport { Button } from './button';\n\nexport function ThemeToggle() {\n  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system';\n    if (savedTheme) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    let newTheme: 'light' | 'dark' | 'system';\n\n    if (theme === 'light') {\n      newTheme = 'dark';\n    } else if (theme === 'dark') {\n      newTheme = 'system';\n    } else {\n      newTheme = 'light';\n    }\n\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n\n    // Apply theme\n    const root = document.documentElement;\n    root.removeAttribute('data-theme');\n\n    if (newTheme === 'dark') {\n      root.setAttribute('data-theme', 'dark');\n    } else if (newTheme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      if (systemTheme === 'dark') {\n        root.setAttribute('data-theme', 'dark');\n      }\n    }\n  };\n\n  const getIcon = () => {\n    if (!mounted) return <Sun className=\"h-4 w-4\" />;\n\n    if (theme === 'system') {\n      return <Monitor className=\"h-4 w-4\" />;\n    }\n    return theme === 'dark' ? <Moon className=\"h-4 w-4\" /> : <Sun className=\"h-4 w-4\" />;\n  };\n\n  const getLabel = () => {\n    if (!mounted) return 'Claro';\n\n    if (theme === 'system') {\n      return 'Sistema';\n    }\n    return theme === 'dark' ? 'Escuro' : 'Claro';\n  };\n\n  if (!mounted) {\n    return (\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"w-auto px-3\"\n        disabled\n      >\n        <Sun className=\"h-4 w-4\" />\n        <span className=\"ml-2 hidden sm:inline\">Claro</span>\n      </Button>\n    );\n  }\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"sm\"\n      onClick={toggleTheme}\n      className=\"w-auto px-3\"\n      aria-label={`Alternar tema. Tema atual: ${getLabel()}`}\n    >\n      {getIcon()}\n      <span className=\"ml-2 hidden sm:inline\">{getLabel()}</span>\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,SAAS;QACX;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;QAEJ,IAAI,UAAU,SAAS;YACrB,WAAW;QACb,OAAO,IAAI,UAAU,QAAQ;YAC3B,WAAW;QACb,OAAO;YACL,WAAW;QACb;QAEA,SAAS;QACT,aAAa,OAAO,CAAC,SAAS;QAE9B,cAAc;QACd,MAAM,OAAO,SAAS,eAAe;QACrC,KAAK,eAAe,CAAC;QAErB,IAAI,aAAa,QAAQ;YACvB,KAAK,YAAY,CAAC,cAAc;QAClC,OAAO,IAAI,aAAa,UAAU;YAChC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YACzF,IAAI,gBAAgB,QAAQ;gBAC1B,KAAK,YAAY,CAAC,cAAc;YAClC;QACF;IACF;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,SAAS,qBAAO,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QAEpC,IAAI,UAAU,UAAU;YACtB,qBAAO,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QACA,OAAO,UAAU,uBAAS,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;iCAAe,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IAC1E;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,SAAS,OAAO;QAErB,IAAI,UAAU,UAAU;YACtB,OAAO;QACT;QACA,OAAO,UAAU,SAAS,WAAW;IACvC;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAK;YACL,WAAU;YACV,QAAQ;;8BAER,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;QACV,cAAY,CAAC,2BAA2B,EAAE,YAAY;;YAErD;0BACD,8OAAC;gBAAK,WAAU;0BAAyB;;;;;;;;;;;;AAG/C", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Home, Bell, User, Settings } from 'lucide-react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { ThemeToggle } from '@/components/ui/theme-toggle';\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: Home },\n    { name: 'Propriedades', href: '/properties', icon: Home },\n    { name: 'Contratos', href: '/contracts', icon: Home },\n    { name: 'Pagamento<PERSON>', href: '/payments', icon: Home },\n    { name: 'Manuten<PERSON>', href: '/maintenance', icon: Settings },\n  ];\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-[var(--color-border)] bg-[var(--background-primary)]/95 backdrop-blur supports-[backdrop-filter]:bg-[var(--background-primary)]/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-lg bg-[var(--color-primary)] flex items-center justify-center\">\n                <Home className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-[var(--text-primary)]\">\n                RentManager\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"px-3 py-2 rounded-md text-sm font-medium text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--surface-hover)] transition-all duration-200\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Right side actions */}\n          <div className=\"flex items-center space-x-2\">\n            <ThemeToggle />\n            \n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-4 w-4\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-[var(--color-error)] rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* User menu */}\n            <Button variant=\"ghost\" size=\"sm\">\n              <User className=\"h-4 w-4\" />\n              <span className=\"ml-2 hidden sm:inline\">João Silva</span>\n            </Button>\n\n            {/* Mobile menu button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"md:hidden\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              aria-label=\"Abrir menu\"\n            >\n              {isMenuOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden border-t border-[var(--color-border)] py-4\">\n            <nav className=\"flex flex-col space-y-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center px-3 py-2 rounded-md text-sm font-medium text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--surface-hover)] transition-all duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <item.icon className=\"h-4 w-4 mr-3\" />\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,mMAAA,CAAA,OAAI;QAAC;QACpD;YAAE,MAAM;YAAgB,MAAM;YAAe,MAAM,mMAAA,CAAA,OAAI;QAAC;QACxD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,mMAAA,CAAA,OAAI;QAAC;QACpD;YAAE,MAAM;YAAc,MAAM;YAAa,MAAM,mMAAA,CAAA,OAAI;QAAC;QACpD;YAAE,MAAM;YAAc,MAAM;YAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAC5D;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;;;;;;sCAOnE,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2IAAA,CAAA,cAAW;;;;;8CAGZ,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;;sDAC1C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAA4H;;;;;;;;;;;;8CAM9I,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;;sDAC3B,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAI1C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc,CAAC;oCAC9B,cAAW;8CAEV,2BAAa,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAAe,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAM/D,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;;kDAE7B,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,KAAK,IAAI;;+BANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAehC", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  Home, \n  Building, \n  FileText, \n  CreditCard, \n  Wrench, \n  Users, \n  BarChart3, \n  Settings,\n  ChevronLeft,\n  ChevronRight\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { useState } from 'react';\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Propriedades', href: '/properties', icon: Building },\n  { name: 'Contratos', href: '/contracts', icon: FileText },\n  { name: 'Pagamentos', href: '/payments', icon: CreditCard },\n  { name: 'Manutenção', href: '/maintenance', icon: Wrench },\n  { name: 'Inquilinos', href: '/tenants', icon: Users },\n  { name: 'Relatórios', href: '/reports', icon: BarChart3 },\n  { name: 'Configurações', href: '/settings', icon: Settings },\n];\n\ninterface SidebarProps {\n  className?: string;\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n\n  return (\n    <div\n      className={cn(\n        'flex flex-col h-full bg-[var(--background-secondary)] border-r border-[var(--color-border)] transition-all duration-300',\n        isCollapsed ? 'w-16' : 'w-64',\n        className\n      )}\n    >\n      {/* Collapse button */}\n      <div className=\"flex justify-end p-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCollapsed(!isCollapsed)}\n          className=\"h-8 w-8 p-0\"\n        >\n          {isCollapsed ? (\n            <ChevronRight className=\"h-4 w-4\" />\n          ) : (\n            <ChevronLeft className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 pb-4\">\n        <ul className=\"space-y-2\">\n          {navigation.map((item) => {\n            const isActive = pathname === item.href;\n            return (\n              <li key={item.name}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 group',\n                    isActive\n                      ? 'bg-[var(--color-primary)] text-white'\n                      : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--surface-hover)]',\n                    isCollapsed && 'justify-center'\n                  )}\n                  title={isCollapsed ? item.name : undefined}\n                >\n                  <item.icon className={cn('h-5 w-5', !isCollapsed && 'mr-3')} />\n                  {!isCollapsed && (\n                    <span className=\"truncate\">{item.name}</span>\n                  )}\n                </Link>\n              </li>\n            );\n          })}\n        </ul>\n      </nav>\n\n      {/* User info */}\n      {!isCollapsed && (\n        <div className=\"p-4 border-t border-[var(--color-border)]\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"h-8 w-8 rounded-full bg-[var(--color-primary)] flex items-center justify-center\">\n              <span className=\"text-sm font-medium text-white\">JS</span>\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-[var(--text-primary)] truncate\">\n                João Silva\n              </p>\n              <p className=\"text-xs text-[var(--text-secondary)] truncate\">\n                Proprietário\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AAlBA;;;;;;;;AAoBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAgB,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC5D;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAa,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC1D;QAAE,MAAM;QAAc,MAAM;QAAgB,MAAM,sMAAA,CAAA,SAAM;IAAC;IACzD;QAAE,MAAM;QAAc,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAc,MAAM;QAAY,MAAM,kNAAA,CAAA,YAAS;IAAC;IACxD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAC5D;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2HACA,cAAc,SAAS,QACvB;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,eAAe,CAAC;oBAC/B,WAAU;8BAET,4BACC,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;6CAExB,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,yCACA,iGACJ,eAAe;gCAEjB,OAAO,cAAc,KAAK,IAAI,GAAG;;kDAEjC,8OAAC,KAAK,IAAI;wCAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,CAAC,eAAe;;;;;;oCACnD,CAAC,6BACA,8OAAC;wCAAK,WAAU;kDAAY,KAAK,IAAI;;;;;;;;;;;;2BAdlC,KAAK,IAAI;;;;;oBAmBtB;;;;;;;;;;;YAKH,CAAC,6BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAiC;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;8CAGvE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { Header } from '@/components/layout/header';\nimport { Sidebar } from '@/components/layout/sidebar';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <div className=\"min-h-screen bg-[var(--background-primary)]\">\n      {/* Mobile Header */}\n      <div className=\"lg:hidden\">\n        <Header />\n      </div>\n      \n      <div className=\"flex h-screen lg:h-auto\">\n        {/* Desktop Sidebar */}\n        <div className=\"hidden lg:flex lg:flex-shrink-0\">\n          <Sidebar />\n        </div>\n        \n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col overflow-hidden\">\n          {/* Desktop Header */}\n          <div className=\"hidden lg:block\">\n            <Header />\n          </div>\n          \n          {/* Page Content */}\n          <main className=\"flex-1 overflow-y-auto p-4 lg:p-6\">\n            <div className=\"max-w-7xl mx-auto\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;0BAGT,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;kCAIV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;0CAIT,8OAAC;gCAAK,WAAU;0CACd,cAAA,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}