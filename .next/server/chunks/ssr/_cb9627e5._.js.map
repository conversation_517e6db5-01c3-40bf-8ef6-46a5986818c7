{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\n/**\n * Utility function to merge class names with clsx\n */\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\n/**\n * Format currency values\n */\nexport function formatCurrency(value: number, currency = 'BRL'): string {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency,\n  }).format(value);\n}\n\n/**\n * Format date values\n */\nexport function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  \n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    ...options,\n  }).format(dateObj);\n}\n\n/**\n * Format relative time (e.g., \"2 days ago\")\n */\nexport function formatRelativeTime(date: Date | string): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n\n  const intervals = [\n    { label: 'ano', seconds: 31536000 },\n    { label: 'mês', seconds: 2592000 },\n    { label: 'semana', seconds: 604800 },\n    { label: 'dia', seconds: 86400 },\n    { label: 'hora', seconds: 3600 },\n    { label: 'minuto', seconds: 60 },\n  ];\n\n  for (const interval of intervals) {\n    const count = Math.floor(diffInSeconds / interval.seconds);\n    if (count >= 1) {\n      return `há ${count} ${interval.label}${count > 1 ? 's' : ''}`;\n    }\n  }\n\n  return 'agora mesmo';\n}\n\n/**\n * Generate a random ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\n\n/**\n * Validate email format\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Validate CPF format (Brazilian tax ID)\n */\nexport function isValidCPF(cpf: string): boolean {\n  const cleanCPF = cpf.replace(/\\D/g, '');\n  \n  if (cleanCPF.length !== 11) return false;\n  if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false;\n\n  let sum = 0;\n  for (let i = 0; i < 9; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (10 - i);\n  }\n  let remainder = (sum * 10) % 11;\n  if (remainder === 10 || remainder === 11) remainder = 0;\n  if (remainder !== parseInt(cleanCPF.charAt(9))) return false;\n\n  sum = 0;\n  for (let i = 0; i < 10; i++) {\n    sum += parseInt(cleanCPF.charAt(i)) * (11 - i);\n  }\n  remainder = (sum * 10) % 11;\n  if (remainder === 10 || remainder === 11) remainder = 0;\n  if (remainder !== parseInt(cleanCPF.charAt(10))) return false;\n\n  return true;\n}\n\n/**\n * Format CPF\n */\nexport function formatCPF(cpf: string): string {\n  const cleanCPF = cpf.replace(/\\D/g, '');\n  return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, '$1.$2.$3-$4');\n}\n\n/**\n * Format phone number\n */\nexport function formatPhone(phone: string): string {\n  const cleanPhone = phone.replace(/\\D/g, '');\n  if (cleanPhone.length === 11) {\n    return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n  }\n  if (cleanPhone.length === 10) {\n    return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n  }\n  return phone;\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Sleep function for async operations\n */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAKO,SAAS,eAAe,KAAa,EAAE,WAAW,KAAK;IAC5D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,WAAW,IAAmB,EAAE,OAAoC;IAClF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,GAAG,OAAO;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,MAAM,YAAY;QAChB;YAAE,OAAO;YAAO,SAAS;QAAS;QAClC;YAAE,OAAO;YAAO,SAAS;QAAQ;QACjC;YAAE,OAAO;YAAU,SAAS;QAAO;QACnC;YAAE,OAAO;YAAO,SAAS;QAAM;QAC/B;YAAE,OAAO;YAAQ,SAAS;QAAK;QAC/B;YAAE,OAAO;YAAU,SAAS;QAAG;KAChC;IAED,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB,SAAS,OAAO;QACzD,IAAI,SAAS,GAAG;YACd,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,SAAS,KAAK,GAAG,QAAQ,IAAI,MAAM,IAAI;QAC/D;IACF;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,WAAW,GAAW;IACpC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IAEpC,IAAI,SAAS,MAAM,KAAK,IAAI,OAAO;IACnC,IAAI,eAAe,IAAI,CAAC,WAAW,OAAO;IAE1C,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,IAAI,YAAY,AAAC,MAAM,KAAM;IAC7B,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,KAAK,OAAO;IAEvD,MAAM;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;QAC3B,OAAO,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;IAC/C;IACA,YAAY,AAAC,MAAM,KAAM;IACzB,IAAI,cAAc,MAAM,cAAc,IAAI,YAAY;IACtD,IAAI,cAAc,SAAS,SAAS,MAAM,CAAC,MAAM,OAAO;IAExD,OAAO;AACT;AAKO,SAAS,UAAU,GAAW;IACnC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;IACpC,OAAO,SAAS,OAAO,CAAC,gCAAgC;AAC1D;AAKO,SAAS,YAAY,KAAa;IACvC,MAAM,aAAa,MAAM,OAAO,CAAC,OAAO;IACxC,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,IAAI,WAAW,MAAM,KAAK,IAAI;QAC5B,OAAO,WAAW,OAAO,CAAC,yBAAyB;IACrD;IACA,OAAO;AACT;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    \n    const variants = {\n      primary: \"bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90 focus-visible:ring-[var(--color-primary)]\",\n      secondary: \"bg-[var(--color-secondary)] text-white hover:bg-[var(--color-secondary)]/90 focus-visible:ring-[var(--color-secondary)]\",\n      outline: \"border border-[var(--color-border)] bg-transparent text-[var(--text-primary)] hover:bg-[var(--surface-hover)] focus-visible:ring-[var(--color-primary)]\",\n      ghost: \"bg-transparent text-[var(--text-primary)] hover:bg-[var(--surface-hover)] focus-visible:ring-[var(--color-primary)]\",\n      destructive: \"bg-[var(--color-error)] text-white hover:bg-[var(--color-error)]/90 focus-visible:ring-[var(--color-error)]\",\n    };\n\n    const sizes = {\n      sm: \"h-8 px-3 text-sm\",\n      md: \"h-10 px-4 text-sm\",\n      lg: \"h-12 px-6 text-base\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-[var(--color-border)] bg-[var(--background-primary)] shadow-[var(--shadow-sm)] transition-all duration-200\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight text-[var(--text-primary)]\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-[var(--text-secondary)]\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uIACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/theme-toggle.tsx <module evaluation>\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oEACA", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/components/ui/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/theme-toggle.tsx\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gDACA", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/workspaces/saas/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { ArrowRight, Building, Users, CreditCard, BarChart3, Shield, Zap, User } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { ThemeToggle } from \"@/components/ui/theme-toggle\";\n\nexport default function Home() {\n  const features = [\n    {\n      icon: Building,\n      title: \"Gestão de Propriedades\",\n      description: \"Cadastre e gerencie apartamentos, casas e kitnets com facilidade.\"\n    },\n    {\n      icon: Users,\n      title: \"Controle de Inquilinos\",\n      description: \"Mantenha informações completas dos inquilinos e histórico de locações.\"\n    },\n    {\n      icon: CreditCard,\n      title: \"Pagamentos\",\n      description: \"Acompanhe pagamentos, gere cobranças e controle inadimplência.\"\n    },\n    {\n      icon: BarChart3,\n      title: \"Relatórios\",\n      description: \"Relatórios detalhados de receitas, ocupação e performance.\"\n    },\n    {\n      icon: Shield,\n      title: \"Segurança\",\n      description: \"Seus dados protegidos com criptografia e backup automático.\"\n    },\n    {\n      icon: Zap,\n      title: \"Automação\",\n      description: \"Automatize cobranças, lembretes e renovações de contratos.\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-[var(--background-primary)]\">\n      {/* Header */}\n      <header className=\"border-b border-[var(--color-border)]\">\n        <div className=\"container mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2 min-w-0\">\n              <div className=\"h-7 w-7 sm:h-8 sm:w-8 rounded-lg bg-[var(--color-primary)] flex items-center justify-center flex-shrink-0\">\n                <Building className=\"h-4 w-4 sm:h-5 sm:w-5 text-white\" />\n              </div>\n              <span className=\"text-lg sm:text-xl font-bold text-[var(--text-primary)] truncate\">\n                RentManager\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-2 sm:space-x-4\">\n              <div className=\"hidden sm:block\">\n                <ThemeToggle />\n              </div>\n              <Link href=\"/login\">\n                <Button variant=\"outline\" size=\"sm\" className=\"hidden xs:flex\">Entrar</Button>\n                <Button variant=\"outline\" size=\"sm\" className=\"xs:hidden p-2\">\n                  <User className=\"h-4 w-4\" />\n                </Button>\n              </Link>\n              <Link href=\"/register\">\n                <Button size=\"sm\">\n                  <span className=\"hidden xs:inline\">Começar Grátis</span>\n                  <span className=\"xs:hidden\">Grátis</span>\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-12 sm:py-16 lg:py-20 px-3 sm:px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--text-primary)] mb-4 sm:mb-6\">\n            Gerencie seus aluguéis\n            <br className=\"hidden sm:block\" />\n            <span className=\"sm:hidden\"> </span>\n            <span className=\"text-[var(--color-primary)]\">com simplicidade</span>\n          </h1>\n          <p className=\"text-base sm:text-lg lg:text-xl text-[var(--text-secondary)] mb-6 sm:mb-8 max-w-2xl mx-auto px-4\">\n            Sistema completo para proprietários gerenciarem propriedades, contratos,\n            pagamentos e inquilinos em uma única plataforma.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto\">\n            <Link href=\"/register\" className=\"w-full sm:w-auto\">\n              <Button size=\"lg\" className=\"w-full sm:w-auto\">\n                <span className=\"hidden xs:inline\">Começar Grátis</span>\n                <span className=\"xs:hidden\">Grátis</span>\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Button>\n            </Link>\n            <Link href=\"/demo\" className=\"w-full sm:w-auto\">\n              <Button variant=\"outline\" size=\"lg\" className=\"w-full sm:w-auto\">\n                <span className=\"hidden xs:inline\">Ver Demonstração</span>\n                <span className=\"xs:hidden\">Demo</span>\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-12 sm:py-16 lg:py-20 px-3 sm:px-4 bg-[var(--background-secondary)]\">\n        <div className=\"container mx-auto\">\n          <div className=\"text-center mb-8 sm:mb-12 lg:mb-16\">\n            <h2 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-[var(--text-primary)] mb-3 sm:mb-4\">\n              Tudo que você precisa\n            </h2>\n            <p className=\"text-base sm:text-lg text-[var(--text-secondary)] max-w-2xl mx-auto px-4\">\n              Ferramentas completas para simplificar a gestão dos seus imóveis\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8\">\n            {features.map((feature) => (\n              <Card key={feature.title} className=\"hover:shadow-[var(--shadow-md)] transition-all duration-200\">\n                <CardHeader className=\"pb-3 sm:pb-4\">\n                  <div className=\"h-10 w-10 sm:h-12 sm:w-12 rounded-lg bg-[var(--color-primary)]/10 flex items-center justify-center mb-3 sm:mb-4\">\n                    <feature.icon className=\"h-5 w-5 sm:h-6 sm:w-6 text-[var(--color-primary)]\" />\n                  </div>\n                  <CardTitle className=\"text-base sm:text-lg\">{feature.title}</CardTitle>\n                </CardHeader>\n                <CardContent className=\"pt-0\">\n                  <CardDescription className=\"text-sm sm:text-base\">{feature.description}</CardDescription>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-[var(--text-primary)] mb-4\">\n            Pronto para começar?\n          </h2>\n          <p className=\"text-lg text-[var(--text-secondary)] mb-8 max-w-2xl mx-auto\">\n            Junte-se a centenas de proprietários que já simplificaram a gestão dos seus imóveis\n          </p>\n          <Link href=\"/register\">\n            <Button size=\"lg\">\n              Criar Conta Grátis\n              <ArrowRight className=\"ml-2 h-4 w-4\" />\n            </Button>\n          </Link>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"border-t border-[var(--color-border)] py-8 px-4\">\n        <div className=\"container mx-auto text-center\">\n          <p className=\"text-[var(--text-secondary)]\">\n            © 2024 RentManager. Todos os direitos reservados.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAK,WAAU;kDAAmE;;;;;;;;;;;;0CAIrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2IAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAiB;;;;;;0DAC/D,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAC5C,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;;8DACX,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAiG;8CAE7G,8OAAC;oCAAG,WAAU;;;;;;8CACd,8OAAC;oCAAK,WAAU;8CAAY;;;;;;8CAC5B,8OAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;sCAEhD,8OAAC;4BAAE,WAAU;sCAAmG;;;;;;sCAIhH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;0DAC1B,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG1B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAC3B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqF;;;;;;8CAGnG,8OAAC;oCAAE,WAAU;8CAA2E;;;;;;;;;;;;sCAK1F,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,gIAAA,CAAA,OAAI;oCAAqB,WAAU;;sDAClC,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAwB,QAAQ,KAAK;;;;;;;;;;;;sDAE5D,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAwB,QAAQ,WAAW;;;;;;;;;;;;mCAR/D,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BAiBhC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiE;;;;;;sCAG/E,8OAAC;4BAAE,WAAU;sCAA8D;;;;;;sCAG3E,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;;oCAAK;kDAEhB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}]}