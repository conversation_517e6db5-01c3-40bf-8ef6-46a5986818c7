# RentManager - Sistema de Gestão de Aluguéis

Um SaaS completo para gestão de propriedades, contratos, pagamentos e inquilinos, desenvolvido com Next.js 15, TypeScript e Tailwind CSS.

## 🚀 Funcionalidades

### ✅ Implementadas
- **Sistema de Temas**: Modo claro/escuro com suporte a preferências do sistema
- **Interface Responsiva**: Design adaptável para desktop, tablet e mobile
- **Componentes UI**: Biblioteca de componentes reutilizáveis
- **Navegação**: Header e sidebar com navegação intuitiva
- **Dashboard**: Visão geral com métricas e atividades recentes
- **Gestão de Propriedades**: Listagem e visualização de imóveis
- **Autenticação**: Páginas de login e registro
- **Acessibilidade**: Conformidade com WCAG 2.1

### 🔄 Em Desenvolvimento
- **Backend**: API com Node.js e PostgreSQL
- **Autenticação JWT**: Sistema completo de autenticação
- **CRUD Completo**: Operações para todas as entidades
- **Sistema de Pagamentos**: Integração com gateways de pagamento
- **Relatórios**: Dashboards e relatórios detalhados
- **Notificações**: Sistema de alertas e lembretes
- **Upload de Imagens**: Gestão de fotos das propriedades

## 🛠️ Tecnologias

- **Frontend**: Next.js 15 (App Router)
- **Linguagem**: TypeScript
- **Estilização**: Tailwind CSS v4
- **Ícones**: Lucide React
- **Banco de Dados**: PostgreSQL (planejado)
- **ORM**: Knex.js (planejado)
- **Autenticação**: JWT (planejado)

## 🎨 Design System

### Cores do Tema Claro
- **Fundo Principal**: `#FFFFFF`
- **Fundo Secundário**: `#F8FAFC`
- **Texto Principal**: `#1E293B`
- **Texto Secundário**: `#64748B`
- **Primária**: `#3B82F6`
- **Secundária**: `#8B5CF6`
- **Destaque**: `#22C55E`
- **Erro**: `#EF4444`

### Cores do Tema Escuro
- **Fundo Principal**: `#0F172A`
- **Fundo Secundário**: `#1E293B`
- **Texto Principal**: `#F8FAFC`
- **Texto Secundário**: `#CBD5E1`
- **Primária**: `#3B82F6`
- **Secundária**: `#8B5CF6`
- **Destaque**: `#22C55E`
- **Erro**: `#EF4444`

## 🚀 Como Executar

### Pré-requisitos
- Node.js 18+
- npm, yarn ou pnpm

### Instalação

1. Clone o repositório:
```bash
git clone <repository-url>
cd saas
```

2. Instale as dependências:
```bash
npm install
```

3. Execute o servidor de desenvolvimento:
```bash
npm run dev
```

4. Abra [http://localhost:3000](http://localhost:3000) no seu navegador

## 📁 Estrutura do Projeto

```
src/
├── app/                    # App Router do Next.js
│   ├── (auth)/            # Grupo de rotas de autenticação
│   │   ├── login/         # Página de login
│   │   └── register/      # Página de registro
│   ├── (dashboard)/       # Grupo de rotas do dashboard
│   │   ├── dashboard/     # Dashboard principal
│   │   └── properties/    # Gestão de propriedades
│   ├── globals.css        # Estilos globais e variáveis CSS
│   ├── layout.tsx         # Layout raiz
│   └── page.tsx           # Página inicial
├── components/            # Componentes reutilizáveis
│   ├── layout/           # Componentes de layout
│   │   ├── header.tsx    # Cabeçalho
│   │   └── sidebar.tsx   # Barra lateral
│   └── ui/               # Componentes de interface
│       ├── button.tsx    # Botão
│       ├── card.tsx      # Card
│       ├── input.tsx     # Input
│       └── theme-toggle.tsx # Alternador de tema
├── lib/                  # Utilitários e configurações
│   ├── theme.ts          # Provider de tema
│   └── utils.ts          # Funções utilitárias
└── types/                # Definições de tipos TypeScript
    └── index.ts          # Tipos principais
```

## 🎯 Próximos Passos

1. **Configurar Banco de Dados**
   - Setup PostgreSQL
   - Configurar Knex.js
   - Criar migrations

2. **Implementar Backend**
   - API Routes do Next.js
   - Autenticação JWT
   - Middleware de proteção

3. **CRUD Completo**
   - Propriedades
   - Contratos
   - Pagamentos
   - Usuários

4. **Funcionalidades Avançadas**
   - Upload de imagens
   - Sistema de notificações
   - Relatórios e dashboards
   - Integração com pagamentos

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 📞 Contato

Para dúvidas ou sugestões, entre em contato através do email: <EMAIL>
