'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Menu, X, Home, Bell, User, Settings } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ui/theme-toggle';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Propriedades', href: '/properties', icon: Home },
    { name: 'Contratos', href: '/contracts', icon: Home },
    { name: 'Pagamento<PERSON>', href: '/payments', icon: Home },
    { name: 'Manuten<PERSON>', href: '/maintenance', icon: Settings },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b border-[var(--color-border)] bg-[var(--background-primary)]/95 backdrop-blur supports-[backdrop-filter]:bg-[var(--background-primary)]/60">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6">
        <div className="flex h-14 sm:h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center min-w-0">
            <Link href="/" className="flex items-center space-x-2 min-w-0">
              <div className="h-7 w-7 sm:h-8 sm:w-8 rounded-lg bg-[var(--color-primary)] flex items-center justify-center flex-shrink-0">
                <Home className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
              </div>
              <span className="text-lg sm:text-xl font-bold text-[var(--text-primary)] truncate">
                RentManager
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="px-2 xl:px-3 py-2 rounded-md text-sm font-medium text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--surface-hover)] transition-all duration-200 whitespace-nowrap"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right side actions */}
          <div className="flex items-center space-x-1 sm:space-x-2">
            <div className="hidden sm:block">
              <ThemeToggle />
            </div>

            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative p-2">
              <Bell className="h-4 w-4" />
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-[var(--color-error)] rounded-full text-xs flex items-center justify-center text-white">
                3
              </span>
            </Button>

            {/* User menu - Hidden on mobile */}
            <Button variant="ghost" size="sm" className="hidden sm:flex p-2">
              <User className="h-4 w-4" />
              <span className="ml-2 hidden md:inline">João Silva</span>
            </Button>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden p-2"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Abrir menu"
            >
              {isMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-[var(--color-border)] py-3 sm:py-4 animate-fadeIn">
            <nav className="flex flex-col space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center px-3 py-3 rounded-md text-base font-medium text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--surface-hover)] transition-all duration-200 active:bg-[var(--surface-active)]"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <item.icon className="h-5 w-5 mr-3 flex-shrink-0" />
                  <span className="truncate">{item.name}</span>
                </Link>
              ))}

              {/* Mobile-only items */}
              <div className="border-t border-[var(--color-border)] mt-3 pt-3 space-y-1">
                <div className="flex items-center px-3 py-3">
                  <User className="h-5 w-5 mr-3 flex-shrink-0 text-[var(--text-secondary)]" />
                  <span className="text-base font-medium text-[var(--text-primary)] truncate">João Silva</span>
                </div>

                <div className="px-3 py-2">
                  <ThemeToggle />
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
