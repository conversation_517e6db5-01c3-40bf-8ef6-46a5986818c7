'use client';

import { useState, useEffect } from 'react';
import { Moon, Sun, Monitor } from 'lucide-react';
import { Button } from './button';

export function ThemeToggle() {
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system';
    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  const toggleTheme = () => {
    let newTheme: 'light' | 'dark' | 'system';

    if (theme === 'light') {
      newTheme = 'dark';
    } else if (theme === 'dark') {
      newTheme = 'system';
    } else {
      newTheme = 'light';
    }

    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);

    // Apply theme
    const root = document.documentElement;
    root.removeAttribute('data-theme');

    if (newTheme === 'dark') {
      root.setAttribute('data-theme', 'dark');
    } else if (newTheme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      if (systemTheme === 'dark') {
        root.setAttribute('data-theme', 'dark');
      }
    }
  };

  const getIcon = () => {
    if (!mounted) return <Sun className="h-4 w-4" />;

    if (theme === 'system') {
      return <Monitor className="h-4 w-4" />;
    }
    return theme === 'dark' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />;
  };

  const getLabel = () => {
    if (!mounted) return 'Claro';

    if (theme === 'system') {
      return 'Sistema';
    }
    return theme === 'dark' ? 'Escuro' : 'Claro';
  };

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className="w-auto px-3"
        disabled
      >
        <Sun className="h-4 w-4" />
        <span className="ml-2 hidden sm:inline">Claro</span>
      </Button>
    );
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="w-auto px-3"
      aria-label={`Alternar tema. Tema atual: ${getLabel()}`}
    >
      {getIcon()}
      <span className="ml-2 hidden sm:inline">{getLabel()}</span>
    </Button>
  );
}
