@import "tailwindcss";

:root {
  /* Light Mode Colors */
  --background-primary: #FFFFFF;
  --background-secondary: #F8FAFC;
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --color-primary: #3B82F6;
  --color-secondary: #8B5CF6;
  --color-accent: #22C55E;
  --color-error: #EF4444;
  --color-warning: #F59E0B;
  --color-info: #06B6D4;

  /* Border and Surface Colors */
  --border-color: #E2E8F0;
  --surface-hover: #F1F5F9;
  --surface-active: #E2E8F0;

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

[data-theme="dark"] {
  /* Dark Mode Colors */
  --background-primary: #0F172A;
  --background-secondary: #1E293B;
  --text-primary: #F8FAFC;
  --text-secondary: #CBD5E1;
  --color-primary: #3B82F6;
  --color-secondary: #8B5CF6;
  --color-accent: #22C55E;
  --color-error: #EF4444;
  --color-warning: #F59E0B;
  --color-info: #06B6D4;

  /* Border and Surface Colors */
  --border-color: #334155;
  --surface-hover: #334155;
  --surface-active: #475569;

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
}

@theme inline {
  --color-background-primary: var(--background-primary);
  --color-background-secondary: var(--background-secondary);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-accent: var(--color-accent);
  --color-error: var(--color-error);
  --color-warning: var(--color-warning);
  --color-info: var(--color-info);
  --color-border: var(--border-color);
  --color-surface-hover: var(--surface-hover);
  --color-surface-active: var(--surface-active);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background-primary);
  color: var(--text-primary);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--surface-hover);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}
