import Link from "next/link";
import { ArrowRight, Building, Users, CreditCard, BarChart3, Shield, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ui/theme-toggle";

export default function Home() {
  const features = [
    {
      icon: Building,
      title: "Gestão de Propriedades",
      description: "Cadastre e gerencie apartamentos, casas e kitnets com facilidade."
    },
    {
      icon: Users,
      title: "Controle de Inquilinos",
      description: "Mantenha informações completas dos inquilinos e histórico de locações."
    },
    {
      icon: CreditCard,
      title: "Pagamentos",
      description: "Acompanhe pagamentos, gere cobranças e controle inadimplência."
    },
    {
      icon: BarChart3,
      title: "Relatórios",
      description: "Relatórios detalhados de receitas, ocupação e performance."
    },
    {
      icon: Shield,
      title: "Segurança",
      description: "Seus dados protegidos com criptografia e backup automático."
    },
    {
      icon: Zap,
      title: "Automação",
      description: "Automatize cobranças, lembretes e renovações de contratos."
    }
  ];

  return (
    <div className="min-h-screen bg-[var(--background-primary)]">
      {/* Header */}
      <header className="border-b border-[var(--color-border)]">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-[var(--color-primary)] flex items-center justify-center">
                <Building className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-[var(--text-primary)]">
                RentManager
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Link href="/login">
                <Button variant="outline">Entrar</Button>
              </Link>
              <Link href="/register">
                <Button>Começar Grátis</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-[var(--text-primary)] mb-6">
            Gerencie seus aluguéis
            <br />
            <span className="text-[var(--color-primary)]">com simplicidade</span>
          </h1>
          <p className="text-xl text-[var(--text-secondary)] mb-8 max-w-2xl mx-auto">
            Sistema completo para proprietários gerenciarem propriedades, contratos,
            pagamentos e inquilinos em uma única plataforma.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button size="lg" className="w-full sm:w-auto">
                Começar Grátis
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link href="/demo">
              <Button variant="outline" size="lg" className="w-full sm:w-auto">
                Ver Demonstração
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-[var(--background-secondary)]">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--text-primary)] mb-4">
              Tudo que você precisa
            </h2>
            <p className="text-lg text-[var(--text-secondary)] max-w-2xl mx-auto">
              Ferramentas completas para simplificar a gestão dos seus imóveis
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature) => (
              <Card key={feature.title} className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
                <CardHeader>
                  <div className="h-12 w-12 rounded-lg bg-[var(--color-primary)]/10 flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-[var(--color-primary)]" />
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[var(--text-primary)] mb-4">
            Pronto para começar?
          </h2>
          <p className="text-lg text-[var(--text-secondary)] mb-8 max-w-2xl mx-auto">
            Junte-se a centenas de proprietários que já simplificaram a gestão dos seus imóveis
          </p>
          <Link href="/register">
            <Button size="lg">
              Criar Conta Grátis
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-[var(--color-border)] py-8 px-4">
        <div className="container mx-auto text-center">
          <p className="text-[var(--text-secondary)]">
            © 2024 RentManager. Todos os direitos reservados.
          </p>
        </div>
      </footer>
    </div>
  );
}
