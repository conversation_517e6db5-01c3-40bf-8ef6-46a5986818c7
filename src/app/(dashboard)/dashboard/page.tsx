'use client';

import {
  Building,
  Users,
  CreditCard,
  TrendingUp,
  AlertTriangle,
  Calendar,
  DollarSign,
  Home
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatDate } from '@/lib/utils';

export default function DashboardPage() {
  // Mock data - em produção viria de uma API
  const stats = {
    totalProperties: 12,
    occupiedProperties: 10,
    totalRevenue: 45000,
    pendingPayments: 3,
    maintenanceRequests: 2,
    occupancyRate: 83.3
  };

  const recentActivities = [
    {
      id: '1',
      type: 'payment',
      description: 'Pagamento recebido - Apt 101',
      amount: 2500,
      timestamp: new Date('2024-01-15T10:30:00'),
      status: 'success'
    },
    {
      id: '2',
      type: 'maintenance',
      description: 'Nova solicitação de manutenção - Casa 205',
      timestamp: new Date('2024-01-15T09:15:00'),
      status: 'pending'
    },
    {
      id: '3',
      type: 'contract',
      description: 'Contrato renovado - Kitnet 302',
      timestamp: new Date('2024-01-14T16:45:00'),
      status: 'success'
    },
    {
      id: '4',
      type: 'payment',
      description: 'Pagamento em atraso - Apt 203',
      amount: 1800,
      timestamp: new Date('2024-01-14T14:20:00'),
      status: 'warning'
    }
  ];

  const upcomingPayments = [
    {
      id: '1',
      tenant: 'Maria Silva',
      property: 'Apartamento 101',
      amount: 2500,
      dueDate: new Date('2024-01-20'),
      status: 'pending'
    },
    {
      id: '2',
      tenant: 'João Santos',
      property: 'Casa 205',
      amount: 3200,
      dueDate: new Date('2024-01-22'),
      status: 'pending'
    },
    {
      id: '3',
      tenant: 'Ana Costa',
      property: 'Kitnet 302',
      amount: 1200,
      dueDate: new Date('2024-01-25'),
      status: 'pending'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <CreditCard className="h-4 w-4" />;
      case 'maintenance':
        return <AlertTriangle className="h-4 w-4" />;
      case 'contract':
        return <Users className="h-4 w-4" />;
      default:
        return <Home className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-[var(--color-accent)]';
      case 'warning':
        return 'text-[var(--color-warning)]';
      case 'pending':
        return 'text-[var(--color-info)]';
      default:
        return 'text-[var(--text-secondary)]';
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="min-w-0">
          <h1 className="text-xl sm:text-2xl font-bold text-[var(--text-primary)] truncate">Dashboard</h1>
          <p className="text-sm sm:text-base text-[var(--text-secondary)] mt-1">
            Visão geral dos seus imóveis e atividades
          </p>
        </div>
        <div className="flex-shrink-0">
          <Button className="w-full sm:w-auto">
            <Building className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">Nova Propriedade</span>
            <span className="xs:hidden">Nova</span>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        <Card className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium truncate pr-2">Total de Propriedades</CardTitle>
            <Building className="h-4 w-4 text-[var(--text-secondary)] flex-shrink-0" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold text-[var(--text-primary)]">
              {stats.totalProperties}
            </div>
            <p className="text-xs text-[var(--text-secondary)] truncate">
              {stats.occupiedProperties} ocupadas
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium truncate pr-2">Receita Total</CardTitle>
            <DollarSign className="h-4 w-4 text-[var(--text-secondary)] flex-shrink-0" />
          </CardHeader>
          <CardContent>
            <div className="text-lg sm:text-2xl font-bold text-[var(--text-primary)] truncate">
              {formatCurrency(stats.totalRevenue)}
            </div>
            <p className="text-xs text-[var(--color-accent)] truncate">
              +12% em relação ao mês anterior
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium truncate pr-2">Pagamentos Pendentes</CardTitle>
            <CreditCard className="h-4 w-4 text-[var(--text-secondary)] flex-shrink-0" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold text-[var(--text-primary)]">
              {stats.pendingPayments}
            </div>
            <p className="text-xs text-[var(--color-warning)] truncate">
              Requer atenção
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium truncate pr-2">Taxa de Ocupação</CardTitle>
            <TrendingUp className="h-4 w-4 text-[var(--text-secondary)] flex-shrink-0" />
          </CardHeader>
          <CardContent>
            <div className="text-xl sm:text-2xl font-bold text-[var(--text-primary)]">
              {stats.occupancyRate}%
            </div>
            <p className="text-xs text-[var(--color-accent)] truncate">
              Acima da média
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        {/* Recent Activities */}
        <Card className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Atividades Recentes</CardTitle>
            <CardDescription className="text-sm">
              Últimas movimentações em suas propriedades
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 sm:space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3 sm:space-x-4">
                  <div className={`p-2 rounded-full bg-[var(--background-secondary)] ${getStatusColor(activity.status)} flex-shrink-0`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-[var(--text-primary)] truncate">
                      {activity.description}
                    </p>
                    <p className="text-xs text-[var(--text-secondary)]">
                      {formatDate(activity.timestamp, {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  {activity.amount && (
                    <div className="text-sm font-medium text-[var(--text-primary)] flex-shrink-0">
                      <span className="hidden sm:inline">{formatCurrency(activity.amount)}</span>
                      <span className="sm:hidden">{formatCurrency(activity.amount).replace('R$', 'R$')}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Payments */}
        <Card className="hover:shadow-[var(--shadow-md)] transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-base sm:text-lg">Próximos Pagamentos</CardTitle>
            <CardDescription className="text-sm">
              Pagamentos esperados nos próximos dias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 sm:space-y-4">
              {upcomingPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between space-x-3">
                  <div className="flex items-center space-x-3 min-w-0 flex-1">
                    <div className="p-2 rounded-full bg-[var(--color-primary)]/10 flex-shrink-0">
                      <Calendar className="h-4 w-4 text-[var(--color-primary)]" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-[var(--text-primary)] truncate">
                        {payment.tenant}
                      </p>
                      <p className="text-xs text-[var(--text-secondary)] truncate">
                        {payment.property}
                      </p>
                    </div>
                  </div>
                  <div className="text-right flex-shrink-0">
                    <p className="text-sm font-medium text-[var(--text-primary)]">
                      <span className="hidden sm:inline">{formatCurrency(payment.amount)}</span>
                      <span className="sm:hidden">{formatCurrency(payment.amount).replace('R$', 'R$')}</span>
                    </p>
                    <p className="text-xs text-[var(--text-secondary)]">
                      {formatDate(payment.dueDate)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
