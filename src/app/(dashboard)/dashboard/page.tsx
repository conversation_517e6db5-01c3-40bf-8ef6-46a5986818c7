'use client';

import { 
  Building, 
  Users, 
  CreditCard, 
  TrendingUp, 
  AlertTriangle,
  Calendar,
  DollarSign,
  Home
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatDate } from '@/lib/utils';

export default function DashboardPage() {
  // Mock data - em produção viria de uma API
  const stats = {
    totalProperties: 12,
    occupiedProperties: 10,
    totalRevenue: 45000,
    pendingPayments: 3,
    maintenanceRequests: 2,
    occupancyRate: 83.3
  };

  const recentActivities = [
    {
      id: '1',
      type: 'payment',
      description: 'Pagamento recebido - Apt 101',
      amount: 2500,
      timestamp: new Date('2024-01-15T10:30:00'),
      status: 'success'
    },
    {
      id: '2',
      type: 'maintenance',
      description: 'Nova solicitação de manutenção - Casa 205',
      timestamp: new Date('2024-01-15T09:15:00'),
      status: 'pending'
    },
    {
      id: '3',
      type: 'contract',
      description: 'Contrato renovado - Kitnet 302',
      timestamp: new Date('2024-01-14T16:45:00'),
      status: 'success'
    },
    {
      id: '4',
      type: 'payment',
      description: 'Pagamento em atraso - Apt 203',
      amount: 1800,
      timestamp: new Date('2024-01-14T14:20:00'),
      status: 'warning'
    }
  ];

  const upcomingPayments = [
    {
      id: '1',
      tenant: 'Maria Silva',
      property: 'Apartamento 101',
      amount: 2500,
      dueDate: new Date('2024-01-20'),
      status: 'pending'
    },
    {
      id: '2',
      tenant: 'João Santos',
      property: 'Casa 205',
      amount: 3200,
      dueDate: new Date('2024-01-22'),
      status: 'pending'
    },
    {
      id: '3',
      tenant: 'Ana Costa',
      property: 'Kitnet 302',
      amount: 1200,
      dueDate: new Date('2024-01-25'),
      status: 'pending'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'payment':
        return <CreditCard className="h-4 w-4" />;
      case 'maintenance':
        return <AlertTriangle className="h-4 w-4" />;
      case 'contract':
        return <Users className="h-4 w-4" />;
      default:
        return <Home className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-[var(--color-accent)]';
      case 'warning':
        return 'text-[var(--color-warning)]';
      case 'pending':
        return 'text-[var(--color-info)]';
      default:
        return 'text-[var(--text-secondary)]';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-[var(--text-primary)]">Dashboard</h1>
          <p className="text-[var(--text-secondary)] mt-1">
            Visão geral dos seus imóveis e atividades
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Button>
            <Building className="h-4 w-4 mr-2" />
            Nova Propriedade
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Propriedades</CardTitle>
            <Building className="h-4 w-4 text-[var(--text-secondary)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--text-primary)]">
              {stats.totalProperties}
            </div>
            <p className="text-xs text-[var(--text-secondary)]">
              {stats.occupiedProperties} ocupadas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Receita Total</CardTitle>
            <DollarSign className="h-4 w-4 text-[var(--text-secondary)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--text-primary)]">
              {formatCurrency(stats.totalRevenue)}
            </div>
            <p className="text-xs text-[var(--color-accent)]">
              +12% em relação ao mês anterior
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pagamentos Pendentes</CardTitle>
            <CreditCard className="h-4 w-4 text-[var(--text-secondary)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--text-primary)]">
              {stats.pendingPayments}
            </div>
            <p className="text-xs text-[var(--color-warning)]">
              Requer atenção
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Ocupação</CardTitle>
            <TrendingUp className="h-4 w-4 text-[var(--text-secondary)]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[var(--text-primary)]">
              {stats.occupancyRate}%
            </div>
            <p className="text-xs text-[var(--color-accent)]">
              Acima da média
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Atividades Recentes</CardTitle>
            <CardDescription>
              Últimas movimentações em suas propriedades
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4">
                  <div className={`p-2 rounded-full bg-[var(--background-secondary)] ${getStatusColor(activity.status)}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-[var(--text-primary)] truncate">
                      {activity.description}
                    </p>
                    <p className="text-xs text-[var(--text-secondary)]">
                      {formatDate(activity.timestamp, { 
                        day: '2-digit', 
                        month: '2-digit', 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </p>
                  </div>
                  {activity.amount && (
                    <div className="text-sm font-medium text-[var(--text-primary)]">
                      {formatCurrency(activity.amount)}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Payments */}
        <Card>
          <CardHeader>
            <CardTitle>Próximos Pagamentos</CardTitle>
            <CardDescription>
              Pagamentos esperados nos próximos dias
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-full bg-[var(--color-primary)]/10">
                      <Calendar className="h-4 w-4 text-[var(--color-primary)]" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[var(--text-primary)]">
                        {payment.tenant}
                      </p>
                      <p className="text-xs text-[var(--text-secondary)]">
                        {payment.property}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-[var(--text-primary)]">
                      {formatCurrency(payment.amount)}
                    </p>
                    <p className="text-xs text-[var(--text-secondary)]">
                      {formatDate(payment.dueDate)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
