'use client';

import { useState } from 'react';
import {
  Building,
  Plus,
  Search,
  Filter,
  MapPin,
  Bed,
  Bath,
  Square,
  Car,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { formatCurrency } from '@/lib/utils';
import type { Property } from '@/types';

export default function PropertiesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  // Mock data - em produção viria de uma API
  const properties: Property[] = [
    {
      id: '1',
      title: 'Apartamento Moderno no Centro',
      description: 'Apartamento totalmente mobiliado com vista para a cidade',
      type: 'apartment',
      address: {
        street: 'Rua das Flores',
        number: '123',
        neighborhood: 'Centro',
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-567',
        country: 'Brasil'
      },
      features: {
        bedrooms: 2,
        bathrooms: 2,
        area: 85,
        parking: 1,
        furnished: true,
        petFriendly: false,
        hasElevator: true,
        hasPool: true,
        hasGym: true
      },
      images: ['/placeholder-property.jpg'],
      ownerId: 'owner1',
      status: 'rented',
      monthlyRent: 2500,
      deposit: 5000,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: '2',
      title: 'Casa Familiar com Jardim',
      description: 'Casa espaçosa com quintal e área gourmet',
      type: 'house',
      address: {
        street: 'Rua dos Ipês',
        number: '456',
        neighborhood: 'Jardim América',
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-890',
        country: 'Brasil'
      },
      features: {
        bedrooms: 3,
        bathrooms: 3,
        area: 150,
        parking: 2,
        furnished: false,
        petFriendly: true,
        hasGarden: true,
        hasBalcony: true
      },
      images: ['/placeholder-property.jpg'],
      ownerId: 'owner1',
      status: 'available',
      monthlyRent: 4200,
      deposit: 8400,
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-10')
    },
    {
      id: '3',
      title: 'Kitnet Estudantil',
      description: 'Kitnet compacta próxima à universidade',
      type: 'kitnet',
      address: {
        street: 'Rua Universitária',
        number: '789',
        neighborhood: 'Vila Universitária',
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-123',
        country: 'Brasil'
      },
      features: {
        bedrooms: 1,
        bathrooms: 1,
        area: 35,
        parking: 0,
        furnished: true,
        petFriendly: false,
        hasInternet: true,
        hasLaundry: true
      },
      images: ['/placeholder-property.jpg'],
      ownerId: 'owner1',
      status: 'rented',
      monthlyRent: 1200,
      deposit: 2400,
      createdAt: new Date('2024-01-08'),
      updatedAt: new Date('2024-01-12')
    }
  ];

  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.address.neighborhood.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || property.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: Property['status']) => {
    switch (status) {
      case 'available':
        return 'bg-[var(--color-accent)]/10 text-[var(--color-accent)]';
      case 'rented':
        return 'bg-[var(--color-primary)]/10 text-[var(--color-primary)]';
      case 'maintenance':
        return 'bg-[var(--color-warning)]/10 text-[var(--color-warning)]';
      case 'inactive':
        return 'bg-[var(--text-secondary)]/10 text-[var(--text-secondary)]';
      default:
        return 'bg-[var(--text-secondary)]/10 text-[var(--text-secondary)]';
    }
  };

  const getStatusText = (status: Property['status']) => {
    switch (status) {
      case 'available':
        return 'Disponível';
      case 'rented':
        return 'Alugado';
      case 'maintenance':
        return 'Manutenção';
      case 'inactive':
        return 'Inativo';
      default:
        return status;
    }
  };

  const getTypeText = (type: Property['type']) => {
    switch (type) {
      case 'apartment':
        return 'Apartamento';
      case 'house':
        return 'Casa';
      case 'kitnet':
        return 'Kitnet';
      case 'commercial':
        return 'Comercial';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="min-w-0">
          <h1 className="text-xl sm:text-2xl font-bold text-[var(--text-primary)] truncate">Propriedades</h1>
          <p className="text-sm sm:text-base text-[var(--text-secondary)] mt-1">
            Gerencie todas as suas propriedades
          </p>
        </div>
        <div className="flex-shrink-0">
          <Button className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">Nova Propriedade</span>
            <span className="xs:hidden">Nova</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-3 sm:p-4 lg:p-6">
          <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--text-secondary)]" />
                <Input
                  placeholder="Buscar por título ou bairro..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex flex-col xs:flex-row gap-2">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="flex-1 xs:flex-none px-3 py-2 border border-[var(--color-border)] rounded-lg bg-[var(--background-primary)] text-[var(--text-primary)] text-sm focus:outline-none focus:ring-2 focus:ring-[var(--color-primary)] min-w-0"
              >
                <option value="all">Todos os tipos</option>
                <option value="apartment">Apartamento</option>
                <option value="house">Casa</option>
                <option value="kitnet">Kitnet</option>
                <option value="commercial">Comercial</option>
              </select>
              <Button variant="outline" size="sm" className="flex-shrink-0">
                <Filter className="h-4 w-4 mr-2" />
                <span className="hidden xs:inline">Filtros</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Properties Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
        {filteredProperties.map((property) => (
          <Card key={property.id} className="hover:shadow-[var(--shadow-md)] transition-all duration-200 overflow-hidden">
            <CardHeader className="p-0">
              <div className="relative h-40 sm:h-48 bg-[var(--background-secondary)] overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <Building className="h-12 w-12 sm:h-16 sm:w-16 text-[var(--text-secondary)]" />
                </div>
                <div className="absolute top-2 sm:top-3 left-2 sm:left-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(property.status)}`}>
                    {getStatusText(property.status)}
                  </span>
                </div>
                <div className="absolute top-2 sm:top-3 right-2 sm:right-3">
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-[var(--background-primary)]/90 text-[var(--text-primary)]">
                    {getTypeText(property.type)}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-3 sm:p-4">
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-[var(--text-primary)] truncate text-sm sm:text-base">
                    {property.title}
                  </h3>
                  <div className="flex items-center text-xs sm:text-sm text-[var(--text-secondary)] mt-1">
                    <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{property.address.neighborhood}, {property.address.city}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs sm:text-sm text-[var(--text-secondary)]">
                  <div className="flex items-center space-x-2 sm:space-x-3 min-w-0">
                    <div className="flex items-center flex-shrink-0">
                      <Bed className="h-3 w-3 mr-1" />
                      {property.features.bedrooms}
                    </div>
                    <div className="flex items-center flex-shrink-0">
                      <Bath className="h-3 w-3 mr-1" />
                      {property.features.bathrooms}
                    </div>
                    <div className="flex items-center flex-shrink-0">
                      <Square className="h-3 w-3 mr-1" />
                      {property.features.area}m²
                    </div>
                    {property.features.parking > 0 && (
                      <div className="flex items-center flex-shrink-0">
                        <Car className="h-3 w-3 mr-1" />
                        {property.features.parking}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="min-w-0 flex-1">
                    <p className="text-base sm:text-lg font-bold text-[var(--text-primary)] truncate">
                      {formatCurrency(property.monthlyRent)}
                    </p>
                    <p className="text-xs text-[var(--text-secondary)]">
                      por mês
                    </p>
                  </div>
                  <div className="flex space-x-1 flex-shrink-0">
                    <Button variant="ghost" size="sm" className="p-2">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="p-2">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="p-2">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProperties.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Building className="h-12 w-12 text-[var(--text-secondary)] mx-auto mb-4" />
            <h3 className="text-lg font-medium text-[var(--text-primary)] mb-2">
              Nenhuma propriedade encontrada
            </h3>
            <p className="text-[var(--text-secondary)] mb-4">
              Não encontramos propriedades que correspondam aos seus critérios de busca.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Primeira Propriedade
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
