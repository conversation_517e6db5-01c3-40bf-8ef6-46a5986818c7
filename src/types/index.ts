// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'owner' | 'tenant';
  avatar?: string;
  phone?: string;
  cpf?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Property types
export interface Property {
  id: string;
  title: string;
  description: string;
  type: 'apartment' | 'house' | 'kitnet' | 'commercial';
  address: Address;
  features: PropertyFeatures;
  images: string[];
  ownerId: string;
  status: 'available' | 'rented' | 'maintenance' | 'inactive';
  monthlyRent: number;
  deposit: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Address {
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface PropertyFeatures {
  bedrooms: number;
  bathrooms: number;
  area: number; // in square meters
  parking: number;
  furnished: boolean;
  petFriendly: boolean;
  hasElevator?: boolean;
  hasPool?: boolean;
  hasGym?: boolean;
  hasGarden?: boolean;
  hasBalcony?: boolean;
  hasAirConditioning?: boolean;
  hasInternet?: boolean;
  hasLaundry?: boolean;
  hasKitchen?: boolean;
}

// Contract types
export interface Contract {
  id: string;
  propertyId: string;
  tenantId: string;
  ownerId: string;
  startDate: Date;
  endDate: Date;
  monthlyRent: number;
  deposit: number;
  status: 'active' | 'expired' | 'terminated' | 'pending';
  terms: string;
  createdAt: Date;
  updatedAt: Date;
}

// Payment types
export interface Payment {
  id: string;
  contractId: string;
  amount: number;
  dueDate: Date;
  paidDate?: Date;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  type: 'rent' | 'deposit' | 'fee' | 'maintenance';
  description?: string;
  paymentMethod?: 'pix' | 'bank_transfer' | 'credit_card' | 'cash';
  createdAt: Date;
  updatedAt: Date;
}

// Maintenance types
export interface MaintenanceRequest {
  id: string;
  propertyId: string;
  tenantId: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'completed' | 'cancelled';
  category: 'plumbing' | 'electrical' | 'appliance' | 'structural' | 'cleaning' | 'other';
  images?: string[];
  estimatedCost?: number;
  actualCost?: number;
  assignedTo?: string;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  actionUrl?: string;
  createdAt: Date;
}

// Dashboard types
export interface DashboardStats {
  totalProperties: number;
  occupiedProperties: number;
  totalRevenue: number;
  pendingPayments: number;
  maintenanceRequests: number;
  recentActivities: Activity[];
}

export interface Activity {
  id: string;
  type: 'payment' | 'contract' | 'maintenance' | 'property';
  description: string;
  timestamp: Date;
  userId?: string;
  propertyId?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: 'owner' | 'tenant';
  phone?: string;
  cpf?: string;
}

export interface PropertyForm {
  title: string;
  description: string;
  type: Property['type'];
  address: Address;
  features: PropertyFeatures;
  monthlyRent: number;
  deposit: number;
  images: File[];
}

// Filter types
export interface PropertyFilters {
  type?: Property['type'];
  minRent?: number;
  maxRent?: number;
  bedrooms?: number;
  bathrooms?: number;
  minArea?: number;
  maxArea?: number;
  city?: string;
  neighborhood?: string;
  furnished?: boolean;
  petFriendly?: boolean;
  status?: Property['status'];
}

export interface PaymentFilters {
  status?: Payment['status'];
  type?: Payment['type'];
  startDate?: Date;
  endDate?: Date;
  propertyId?: string;
  tenantId?: string;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';
